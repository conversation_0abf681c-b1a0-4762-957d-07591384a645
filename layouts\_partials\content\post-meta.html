{{/* 文章元信息组件

  显示文章标题、发布时间、阅读时间、标签、分类等信息

  @context {page} . 当前文章页面对象
*/}}


<div class="post-meta">
  <!-- 文章标题 -->
  <h1 class="text-foreground mb-6 text-3xl leading-tight font-bold md:text-4xl">
    {{ .Title }}
  </h1>

  <!-- 文章摘要 -->
  {{ if .Params.summary }}
    <div class="text-muted-foreground mb-6 text-lg leading-relaxed">
      {{ .Params.summary | plainify }}
    </div>
  {{ end }}


  <!-- 元信息区域 -->
  <div class="bg-card border-border flex flex-col gap-4 rounded-xl border p-6">
    <!-- 发布时间和阅读时间 -->
    <div
      class="text-muted-foreground flex flex-wrap items-center gap-4 text-sm">
      <!-- 发布时间 -->
      <div class="flex items-center gap-2">
        {{ partial "features/icon.html" (dict "name" "calendar" "size" "sm" "ariaLabel" (i18n "post.published_on")) }}
        <time datetime="{{ .Date.Format "2006-01-02" }}">
          {{ .Date.Format (i18n "time.date_format" | default "2006年01月02日") }}
        </time>
      </div>

      <!-- 更新时间 -->
      {{ if ne .Date .Lastmod }}
        <div class="flex items-center gap-2">
          {{ partial "features/icon.html" (dict "name" "edit" "size" "sm" "ariaLabel" (i18n "post.updated_on")) }}
          <time datetime="{{ .Lastmod.Format "2006-01-02" }}">
            {{ i18n "post.updated_on" | default "更新于" }}
            {{ .Lastmod.Format (i18n "time.date_format" | default "2006年01月02日") }}
          </time>
        </div>
      {{ end }}


      <!-- 阅读时间 -->
      {{ if .ReadingTime }}
        <div class="flex items-center gap-2">
          {{ partial "features/icon.html" (dict "name" "clock" "size" "sm" "ariaLabel" (i18n "post.reading_time")) }}
          <span
            >{{ .ReadingTime }} {{ i18n "time.minute" | default "分钟" }}</span
          >
        </div>
      {{ end }}


      <!-- 字数统计 -->
      {{ if .WordCount }}
        <div class="flex items-center gap-2">
          {{ partial "features/icon.html" (dict "name" "text" "size" "sm" "ariaLabel" "字数统计") }}
          <span>{{ .WordCount }} {{ i18n "post.words" | default "字" }}</span>
        </div>
      {{ end }}

    </div>

    <!-- 分类和标签 -->
    {{ if or .Params.categories .Params.tags }}
      <div class="flex flex-wrap items-center gap-4">
        <!-- 分类 -->
        {{ if .Params.categories }}
          <div class="flex items-center gap-2">
            {{ partial "features/icon.html" (dict "name" "folder" "size" "sm" "ariaLabel" (i18n "post.categories")) }}
            <span class="text-muted-foreground mr-2 text-sm"
              >{{ i18n "post.categories" | default "分类" }}:</span
            >
            <div class="flex flex-wrap gap-2">
              {{ range .Params.categories }}
                <a
                  href="{{ "/categories/" | relURL }}{{ . | urlize }}/"
                  class="bg-primary/10 text-primary hover:bg-primary/20 focus:ring-primary/20 inline-flex items-center rounded-full px-3 py-1 text-sm font-medium transition-all duration-200 ease-out hover:-translate-y-0.5 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-none">
                  {{ . }}
                </a>
              {{ end }}
            </div>
          </div>
        {{ end }}


        <!-- 标签 -->
        {{ if .Params.tags }}
          <div class="flex items-center gap-2">
            {{ partial "features/icon.html" (dict "name" "tag" "size" "sm" "ariaLabel" (i18n "post.tags")) }}
            <span class="text-muted-foreground mr-2 text-sm"
              >{{ i18n "post.tags" | default "标签" }}:</span
            >
            <div class="flex flex-wrap gap-2">
              {{ range .Params.tags }}
                <a
                  href="{{ "/tags/" | relURL }}{{ . | urlize }}/"
                  class="bg-muted/50 text-muted-foreground hover:bg-primary/10 hover:text-primary focus:ring-primary/20 inline-flex items-center rounded px-2 py-1 text-sm transition-all duration-200 ease-out hover:-translate-y-0.5 hover:scale-105 focus:ring-2 focus:outline-none">
                  #{{ . }}
                </a>
              {{ end }}
            </div>
          </div>
        {{ end }}

      </div>
    {{ end }}


    <!-- 作者信息 -->
    {{ if .Params.author }}
      <div class="border-border flex items-center gap-2 border-t pt-2">
        {{ partial "features/icon.html" (dict "name" "user" "size" "sm" "ariaLabel" (i18n "post.author")) }}
        <span class="text-muted-foreground text-sm"
          >{{ i18n "post.author" | default "作者" }}:</span
        >
        <span class="text-foreground text-sm font-medium"
          >{{ .Params.author }}</span
        >
      </div>
    {{ end }}

  </div>
</div>
