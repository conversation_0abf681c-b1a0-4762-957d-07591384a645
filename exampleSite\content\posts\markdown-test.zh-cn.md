---
title: "Markdown 语法测试文档"
date: 2024-01-15T10:00:00+08:00
draft: false
summary: "这是一个包含各种 Markdown 语法的测试文档，用于验证文档样式的完整性。"
categories: ["测试"]
tags: ["markdown", "文档", "样式"]
---

# 一级标题

这是一级标题下的段落。

## 二级标题

这是二级标题下的段落。

### 三级标题

这是三级标题下的段落。

#### 四级标题

这是四级标题下的段落。

##### 五级标题

这是五级标题下的段落。

###### 六级标题

这是六级标题下的段落。

## 段落和文本格式

这是一个普通段落。它可以包含**粗体文本**、*斜体文本*、***粗斜体文本***、~~删除线~~、`行内代码`和[链接文本](https://example.com)。

这是另一个段落，用于测试段落之间的间距。

## 引用块

> 这是一个简单的引用块。
> 
> 引用块可以包含多个段落。

> 这是一个嵌套引用的例子：
> 
> > 这是嵌套引用的内容。
> > 
> > 可以有多层嵌套。

## 列表

### 无序列表

- 第一项
- 第二项
  - 嵌套项 1
  - 嵌套项 2
    - 更深层的嵌套项
- 第三项

### 有序列表

1. 第一项
2. 第二项
   1. 嵌套有序项 1
   2. 嵌套有序项 2
      1. 更深层的嵌套项
3. 第三项

### 任务列表（复选框）

- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务
- [ ] 嵌套任务列表
  - [x] 子任务 1（已完成）
  - [ ] 子任务 2（未完成）
  - [x] 子任务 3（已完成）

### 定义列表

术语 1
: 这是术语 1 的定义。

术语 2
: 这是术语 2 的定义。
: 术语可以有多个定义。

## 代码

### 行内代码

这是一个包含 `console.log('Hello World')` 的段落。

### 代码块

```javascript
function greet(name) {
  console.log(`你好，${name}！`);
}

greet('世界');
```

```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
```

```css
.prose {
  max-width: none;
  color: var(--tw-prose-body);
}

.prose h1 {
  font-size: 2.25rem;
  font-weight: 700;
}
```

## 表格

| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:--------:|-------:|
| 内容 1 | 内容 2   | 内容 3 |
| 较长的内容 | 中等     | 短     |
| 数据 A | 数据 B   | 数据 C |

## 水平分割线

---

## 图片

![示例图片](/images/basic-image.jpg "示例图片")

## 链接

这是一个[普通链接](https://example.com)。

这是一个[带标题的链接](https://example.com "链接标题")。

这是一个引用式链接：[引用链接][1]

[1]: https://example.com "引用链接标题"

## 脚注

这是一个包含脚注的段落[^1]。

这里是另一个脚注[^note]。

[^1]: 这是第一个脚注的内容。

[^note]: 这是命名脚注的内容。

## 高亮文本

这是一个包含==高亮文本==的段落。

## 上标和下标

H~2~O 是水的化学分子式。

E = mc^2^ 是爱因斯坦的质能方程。

## 键盘按键

按 <kbd>Ctrl</kbd> + <kbd>C</kbd> 复制文本。

## 缩写

HTML 是*超文本标记语言*的缩写。

*[HTML]: 超文本标记语言

## 数学公式（如果支持 KaTeX）

行内公式：$E = mc^2$

块级公式：

$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

## 提示框（如果支持）

> [!NOTE]
> 这是一个注意事项。

> [!TIP]
> 这是一个提示。

> [!IMPORTANT]
> 这是重要信息。

> [!WARNING]
> 这是一个警告。

> [!CAUTION]
> 这是一个注意事项。

## 详情（如果支持）

<details>
<summary>点击展开详情</summary>

这是折叠的详细内容。

你可以在这里包含任何 Markdown 语法：

- 列表项
- **粗体文本**
- `代码`

</details>

## 混合内容测试

这个段落包含多种格式：**粗体**、*斜体*、`代码`、[链接](https://example.com)、~~删除线~~、==高亮==。

### 复杂列表

1. 包含**粗体**文本的第一项
   - 包含`代码`的嵌套项
   - 包含[链接](https://example.com)的另一个嵌套项
2. 包含*斜体*文本的第二项
   1. 有序嵌套项
   2. 另一个有序嵌套项
3. 包含~~删除线~~文本的第三项

### 复杂表格

| 功能 | 状态 | 描述 |
|------|:----:|----|
| **粗体** | ✅ | 支持粗体文本 |
| *斜体* | ✅ | 支持斜体 |
| `代码` | ✅ | 支持行内代码 |
| [链接](https://example.com) | ✅ | 支持链接 |
| ~~删除线~~ | ❌ | 需要测试 |

这个测试文档涵盖了大多数常见的 Markdown 语法，可以用来验证文档样式的完整性和美观性。
