{{/* 相关文章组件

  显示与当前文章相关的其他文章

  @context {page} . 当前文章页面对象
*/}}

{{ $related := .Site.RegularPages.Related . | first 3 }}
{{ $showRelated := .Params.showRelated | default .Site.Params.post.showRelated | default true }}

{{ if and $showRelated $related }}
  <section class="related-posts mb-12">
    <!-- 标题 -->
    <div class="mb-6 flex items-center gap-3">
      {{ partial "features/icon.html" (dict "name" "related" "size" "md" "ariaLabel" "") }}
      <h2 class="text-foreground text-2xl font-bold">
        {{ i18n "post.related_posts" | default "相关文章" }}
      </h2>
    </div>

    <!-- 相关文章列表 -->
    <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {{ range $related }}
        {{/* 使用共享的图片处理器 */}}
        {{ partial "content/cover-image-processor.html" . }}
        {{ $coverImage := .Scratch.Get "coverImage" }}
        {{ $coverProcessed := .Scratch.Get "coverProcessed" }}
        {{ $gradients := .Scratch.Get "gradients" }}
        {{ $colorIndex := .Scratch.Get "colorIndex" }}

        <article class="related-post group">
          <a href="{{ .RelPermalink }}" class="block h-full">
            <div
              class="bg-card border-border hover:bg-primary/5 hover:border-primary/20 focus:ring-primary/20 flex h-full flex-col overflow-hidden rounded-xl border transition-all duration-300 ease-out hover:-translate-y-1 hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:outline-none">
              <!-- 文章封面 -->
              {{ partial "content/card-image.html" (dict "page" . "type" "related" "coverImage" $coverImage "gradients" $gradients "colorIndex" $colorIndex) }}

              <!-- 文章信息 -->
              <div class="flex min-h-0 flex-1 flex-col p-4">
                <!-- 文章标题 -->
                <h3
                  class="text-foreground group-hover:text-primary mb-2 line-clamp-2 flex-shrink-0 text-base leading-tight font-semibold transition-colors duration-200">
                  {{ .Title }}
                </h3>

                <!-- 文章摘要 -->
                <div class="mb-3 flex-1">
                  {{ if .Summary }}
                    <p
                      class="text-muted-foreground line-clamp-2 text-sm leading-relaxed">
                      {{ .Summary | plainify | truncate 80 }}
                    </p>
                  {{ else }}
                    <p class="text-muted-foreground/50 text-sm italic">
                      {{ i18n "post.no_summary" | default "暂无摘要" }}
                    </p>
                  {{ end }}
                </div>

                <!-- 文章元信息 -->
                {{ partial "content/card-meta.html" (dict "page" . "type" "related") }}
              </div>
            </div>
          </a>
        </article>
      {{ end }}
    </div>

    <!-- 查看更多相关文章 -->
    {{ if gt (len $related) 3 }}
      <div class="mt-8 text-center">
        <a
          href="{{ "/posts/" | relLangURL }}"
          class="text-primary hover:text-primary/80 focus:ring-primary/20 inline-flex items-center gap-2 rounded-lg px-4 py-2 font-medium transition-all duration-200 ease-out hover:-translate-y-0.5 hover:scale-105 focus:ring-2 focus:outline-none">
          <span
            >{{ i18n "post.view_more_posts" | default "查看更多文章" }}</span
          >
          {{ partial "features/icon.html" (dict "name" "arrow-right" "size" "sm" "ariaLabel" "") }}
        </a>
      </div>
    {{ end }}

  </section>
{{ end }}
