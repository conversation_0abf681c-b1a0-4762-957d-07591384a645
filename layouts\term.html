{{ define "main" }}
  <!-- 面包屑导航 -->
  {{ partial "navigation/breadcrumb.html" . }}


  <!-- 页面标题和描述 -->
  <header class="mb-8">
    <div class="mb-4 flex items-center gap-3">
      {{ if eq .Data.Singular "tag" }}
        {{ partial "features/icon.html" (dict "name" "tag" "size" "lg" "ariaLabel" "") }}
      {{ else if eq .Data.Singular "category" }}
        {{ partial "features/icon.html" (dict "name" "folder" "size" "lg" "ariaLabel" "") }}
      {{ else }}
        {{ partial "features/icon.html" (dict "name" "bookmark" "size" "lg" "ariaLabel" "") }}
      {{ end }}
      <h1 class="text-foreground text-3xl font-bold">
        {{ .Title }}
      </h1>
    </div>

    {{ if .Content }}
      <div class="prose prose-neutral dark:prose-invert mb-6 max-w-none">
        {{ .Content }}
      </div>
    {{ else }}
      <p class="text-muted-foreground mb-6">
        {{ if eq .Data.Singular "tag" }}
          {{ i18n "term.tag_desc" (dict "tag" .Title) | default (printf "标签 \"%s\" 下的所有文章" .Title) }}
        {{ else if eq .Data.Singular "category" }}
          {{ i18n "term.category_desc" (dict "category" .Title) | default (printf "分类 \"%s\" 下的所有文章" .Title) }}
        {{ else }}
          {{ i18n "term.desc" (dict "term" .Title) | default (printf "\"%s\" 下的所有文章" .Title) }}
        {{ end }}
      </p>
    {{ end }}


    <!-- 统计信息 -->
    <div class="text-muted-foreground flex items-center gap-4 text-sm">
      <div class="flex items-center gap-1">
        {{ partial "features/icon.html" (dict "name" "file-text" "size" "sm" "ariaLabel" "") }}
        <span
          >{{ i18n "term.total_posts" (dict "count" (len .Pages)) | default (printf "共 %d 篇文章" (len .Pages)) }}</span
        >
      </div>
      <div class="flex items-center gap-1">
        {{ partial "features/icon.html" (dict "name" "calendar" "size" "sm" "ariaLabel" "") }}
        <span>{{ i18n "term.date_range" | default "按时间排序" }}</span>
      </div>
    </div>
  </header>

  <!-- 文章列表 -->
  {{ $posts := .Pages }}
  {{ if $posts }}
    {{ partial "content/post-list.html" (dict "posts" $posts) }}
  {{ else }}
    <!-- 空状态 -->
    <div class="py-16 text-center">
      <div
        class="bg-muted/50 mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full">
        {{ partial "features/icon.html" (dict "name" "file-text" "size" "xl" "ariaLabel" "") }}
      </div>
      <h2 class="text-foreground mb-3 text-xl font-medium">
        {{ i18n "term.no_posts" | default "暂无文章" }}
      </h2>
      <p class="text-muted-foreground mb-6">
        {{ if eq .Data.Singular "tag" }}
          {{ i18n "term.no_posts_tag" | default "该标签下还没有文章。" }}
        {{ else if eq .Data.Singular "category" }}
          {{ i18n "term.no_posts_category" | default "该分类下还没有文章。" }}
        {{ else }}
          {{ i18n "term.no_posts_desc" | default "该分类下还没有文章。" }}
        {{ end }}
      </p>
      <a
        href="{{ "/" | relURL }}"
        class="bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center gap-2 rounded-xl px-6 py-3 font-medium transition-colors duration-300">
        {{ partial "features/icon.html" (dict "name" "home" "size" "sm" "ariaLabel" "") }}
        <span>{{ i18n "nav.back_home" | default "返回首页" }}</span>
      </a>
    </div>
  {{ end }}

{{ end }}
