{{- if and hugo.IsMultilingual (site.Params.showLanguageSwitch | default true) -}}
{{- $currentLang := .Language.Lang -}}
{{- $currentPage := . -}}

{{- /* 准备语言菜单内容 */ -}}
{{- $languageMenuContent := "" -}}
{{- range site.Languages -}}
  {{- $lang := .Lang -}}
  {{- $langName := .LanguageName -}}
  {{- $isCurrent := eq $lang $currentLang -}}

  {{- /* 获取当前页面在目标语言下的URL */ -}}
  {{- $targetURL := "/" -}}
  {{- if $currentPage.IsTranslated -}}
    {{- range $currentPage.Translations -}}
      {{- if eq .Language.Lang $lang -}}
        {{- $targetURL = .RelPermalink -}}
      {{- end -}}
    {{- end -}}
    {{- if eq $targetURL "/" -}}
      {{- $targetURL = printf "/%s/" $lang -}}
    {{- end -}}
  {{- else -}}
    {{- $targetURL = printf "/%s/" $lang -}}
  {{- end -}}

  {{- $languageMenuContent = printf "%s<a href=\"%s\" class=\"text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground flex w-full items-center rounded-md px-4 py-2 text-sm transition-all duration-200 ease-out focus:outline-none\" role=\"menuitem\"%s><span class=\"font-medium\">%s</span></a>" $languageMenuContent $targetURL (cond $isCurrent " aria-current=\"true\"" "") $langName -}}
{{- end -}}

{{- /* 使用统一的下拉菜单组件 */ -}}
{{- partial "ui/dropdown-button.html" (dict
  "type" "language"
  "icon" "language"
  "ariaLabel" (i18n "language.toggle")
  "buttonId" "language-toggle"
  "dropdownId" "language-dropdown"
  "menuWidth" "w-40"
  "showCondition" true
  "content" (safeHTML $languageMenuContent)
) -}}

{{- end -}}
