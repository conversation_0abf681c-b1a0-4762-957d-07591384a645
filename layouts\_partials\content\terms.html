{{- /*
  For a given taxonomy, renders a list of terms assigned to the page.

  @context {page} page The current page.
  @context {string} taxonomy The taxonomy.

  @example: {{ partial "terms.html" (dict "taxonomy" "tags" "page" .)
}}
*/}}
{{- $page := .page }}
{{- $taxonomy := .taxonomy }}

{{- with $page.GetTerms $taxonomy }}
  {{- $label := (index . 0).Parent.LinkTitle }}
  <div>
    <div>
      {{ if eq $taxonomy "tags" }}
        {{ i18n "post.tags" }}
      {{ else if eq $taxonomy "categories" }}
        {{ i18n "post.categories" }}
      {{ else }}
        {{ $label }}
      {{ end }}:
    </div>
    <ul>
      {{- range . }}
        <li><a href="{{ .RelPermalink }}">{{ .LinkTitle }}</a></li>
      {{- end }}
    </ul>
  </div>
{{- end }}
