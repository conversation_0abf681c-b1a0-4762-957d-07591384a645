{{/* 文章许可证信息组件

  显示文章的版权和许可证信息

  @context {page} . 当前文章页面对象
*/}}

{{/* 许可证开关配置 */}}
{{ $showLicense := .Params.showLicense | default .Site.Params.post.showLicense | default true }}

{{/* 许可证信息配置 - 支持 frontmatter 覆盖 */}}
{{ $licenseConfig := .Site.Params.post.license }}
{{ if .Params.license }}
  {{ $licenseConfig = .Params.license }}
{{ end }}

{{/* 获取许可证各项配置 */}}
{{ $licenseName := $licenseConfig.name | default "CC BY-NC-SA 4.0" }}
{{ $licenseDescription := $licenseConfig.description | default "本作品采用知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议进行许可。" }}
{{ $licenseUrl := $licenseConfig.url | default "https://creativecommons.org/licenses/by-nc-sa/4.0/" }}
{{ $licenseDisplayName := $licenseConfig.displayName | default "知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议" }}

{{ if $showLicense }}
  <div
    class="post-license bg-muted/30 border-border mb-8 rounded-xl border p-6">
    <div class="flex items-start gap-4">
      <!-- 许可证图标 -->
      <div
        class="bg-primary/10 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg">
        {{ partial "features/icon.html" (dict "name" "license" "size" "lg" "ariaLabel" "许可证") }}
      </div>

      <!-- 许可证信息 -->
      <div class="flex-1">
        <h3 class="text-foreground mb-2 text-lg font-semibold">
          {{ i18n "license.title" | default "版权声明" }}
        </h3>

        <div class="text-muted-foreground space-y-2 text-sm">
          <!-- 作者信息 -->
          <p>
            <strong>{{ i18n "license.author" | default "作者" }}:</strong>
            {{ .Params.author | default .Site.Params.author.name | default .Site.Title }}
          </p>

          <!-- 文章链接 -->
          <p>
            <strong>{{ i18n "license.link" | default "链接" }}:</strong>
            <a
              href="{{ .Permalink }}"
              class="text-primary hover:text-primary/80 transition-colors duration-200">
              {{ .Permalink }}
            </a>
          </p>

          <!-- 许可证类型 -->
          <p>
            <strong>{{ i18n "license.type" | default "许可证" }}:</strong>
            {{ if $licenseUrl }}
              <a
                href="{{ $licenseUrl }}"
                target="_blank"
                rel="noopener noreferrer"
                class="text-primary hover:text-primary/80 transition-colors duration-200">
                {{ $licenseDisplayName }}
              </a>
            {{ else }}
              <span class="text-foreground">{{ $licenseDisplayName }}</span>
            {{ end }}
          </p>

          <!-- 许可证说明 -->
          {{ if $licenseDescription }}
            <p class="text-xs">
              {{ $licenseDescription }}
            </p>
          {{ end }}

        </div>
      </div>
    </div>
  </div>
{{ end }}
