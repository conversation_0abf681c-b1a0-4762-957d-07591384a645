/* 组件样式 - 从 main.css 提取 */

@layer components {
  /* 移动端菜单特殊样式 - 仅保留尺寸相关样式 */
  .mobile-menu.dropdown-menu {
    /* 确保在小屏幕上不会超出视口 */
    max-width: calc(100vw - 2rem);
    /* 移动端菜单的最小宽度 */
    min-width: 20rem;
  }

  /* 移动端菜单在超小屏幕上的适配 */
  @media (max-width: 480px) {
    .mobile-menu.dropdown-menu {
      min-width: 18rem;
      max-width: calc(100vw - 1rem);
    }
  }

  /* 通用下拉菜单基础样式 - 仅保留必要的布局样式 */
  .dropdown-menu {
    min-width: 10rem;
    max-height: 20rem;
    overflow-y: auto;
  }

  .search-result-selected {
    background-color: color-mix(
      in srgb,
      var(--color-primary) 10%,
      transparent
    ) !important;
    color: var(--color-primary) !important;
  }

  /* Scrollbar hide utility */
  .scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 阅读进度条样式 */
  .reading-progress-container {
    height: var(--reading-progress-height);
  }

  .reading-progress-bg {
    height: var(--reading-progress-height);
    background-color: var(--reading-progress-bg);
  }

  .reading-progress-bar {
    height: var(--reading-progress-height);
    box-shadow: 0 0 8px var(--reading-progress-shadow);
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 移动端 Dock 文字隐藏 */
  @media (max-width: 480px) {
    #dock span {
      display: none !important;
    }
  }
}
