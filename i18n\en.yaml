# English language file
# Navigation related
nav:
  menu: "Menu"
  home: "Home"
  posts: "Posts"
  tags: "Tags"
  categories: "Categories"
  about: "About"
  contact: "Contact"
  archive: "Archive"
  archives: "Archives"
  search: "Search"
  breadcrumb: "Breadcrumb navigation"
  pagination: "Pagination navigation"
  prev: "Previous"
  next: "Next"
  prev_page: "Previous page"
  next_page: "Next page"
  page_info: "Page {{ .current }} of {{ .total }}"
  total_pages: "{{ .count }} pages total"
  back_home: "Back to Home"
  post_navigation: "Post navigation"

# Theme related
theme:
  toggle: "Toggle theme"
  light: "Light"
  dark: "Dark"
  system: "System"
  auto: "Auto"

# Footer related
footer:
  copyright: "Copyright"
  all_rights_reserved: "All rights reserved"
  about_us: "About Us"
  contact_us: "Contact Us"
  privacy_policy: "Privacy Policy"
  terms_of_service: "Terms of Service"
  rss_feed: "RSS Feed"

# Home related
home:
  recent_posts: "Recent Posts"
  showing_posts: "Showing latest"
  posts: "posts"
  view_all_posts: "View All Posts"

# Post related
post:
  read_more: "Read more"
  published_on: "Published on"
  updated_on: "Updated on"
  tags: "Tags"
  categories: "Categories"
  share: "Share"
  previous: "Previous"
  next: "Next"
  related_posts: "Related Posts"
  reading_time: "Reading time"
  no_posts: "No posts yet"
  no_posts_desc: "No articles have been published yet. Please check back later."
  list_desc: "Here are all published articles, sorted by date in descending order."
  total_posts: "{{ .count }} posts total"
  author: "Author"
  words: "words"
  no_previous: "No previous post"
  no_next: "No next post"
  no_previous_desc: "This is the latest post"
  no_next_desc: "This is the earliest post"
  view_more_posts: "View more posts"
  reading_time_unit: "min"

# Pagination related
pagination:
  previous: "Previous"
  next: "Next"
  page: "Page"
  of: "of"
  pages: "pages"

# Search related
search:
  placeholder: "Search posts..."
  clear: "Clear"
  close: "Close"
  loading: "Searching..."
  empty_title: "Start searching"
  empty_description: "Enter keywords to search articles"
  no_results_title: "No results found"
  no_results_description: "Try using different keywords"
  results_count: "Found %d results"
  match_score: "Relevance"
  hint_navigate: "Navigate"
  hint_select: "Select"
  hint_close: "Close"
  hint_shortcut: "Shortcut"


# Form related
form:
  name: "Name"
  email: "Email"
  message: "Message"
  subject: "Subject"
  send: "Send"
  required: "Required"

# Time related
time:
  minute: "minute"
  minutes: "minutes"
  hour: "hour"
  hours: "hours"
  day: "day"
  days: "days"
  week: "week"
  weeks: "weeks"
  month: "month"
  months: "months"
  year: "year"
  years: "years"
  ago: "ago"
  date_format: "January 02, 2006"
  date_format_short: "Jan 02"
  month_format: "January 2006"

# Archives related
archives:
  desc: "Browse all articles in chronological order and discover what interests you."
  posts_count: "posts"
  no_posts: "No posts yet"
  no_posts_desc: "No posts have been published yet. Please check back later."
  total_posts: "{{ .count }} posts total"
  timeline: "Timeline view"

# Taxonomy related
taxonomy:
  total_tags: "{{ .count }} tags total"
  total_categories: "{{ .count }} categories total"
  total_terms: "{{ .count }} terms total"
  tags_desc: "Browse all tags to discover interesting content."
  categories_desc: "Browse all categories to discover interesting content."
  desc: "Browse all categories to discover interesting content."
  no_terms: "No categories yet"
  no_terms_desc: "No categories have been created yet. Please check back later."

# Taxonomy term pages
term:
  tag_desc: "All posts under tag \"{{ .tag }}\""
  category_desc: "All posts under category \"{{ .category }}\""
  desc: "All posts under \"{{ .term }}\""
  total_posts: "{{ .count }} posts total"
  date_range: "Sorted by date"
  no_posts: "No posts yet"
  no_posts_tag: "No posts under this tag yet."
  no_posts_category: "No posts under this category yet."
  no_posts_desc: "No posts under this category yet."

# Common
common:
  loading: "Loading..."
  error: "Error"
  success: "Success"
  close: "Close"
  open: "Open"
  save: "Save"
  cancel: "Cancel"
  confirm: "Confirm"
  delete: "Delete"
  edit: "Edit"
  view: "View"
  back: "Back"
  more: "More"
  language: "Language"
  site_logo: "Site Logo"

# Language switching
language:
  toggle: "Switch language"

# License related
license:
  title: "Copyright Notice"
  author: "Author"
  link: "Link"
  type: "License"
  cc_by_nc_sa_desc: "This work is licensed under a Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International License. Please attribute the source, use non-commercially, and maintain the same license."
  cc_by_desc: "This work is licensed under a Creative Commons Attribution 4.0 International License. Please attribute the source."

# Comments related
comments:
  title: "Comments"
  not_configured: "Comment system not configured"
  not_configured_desc: "Please enable comment system in site configuration"
  giscus_not_configured: "Giscus comment system not configured"
  disqus_not_configured: "Disqus comment system not configured"
  disqus_noscript: "Please enable JavaScript to view the comments."
  utterances_not_configured: "Utterances comment system not configured"
  waline_not_configured: "Waline comment system not configured"

# Bottom Dock
dock:
  aria_label: "Quick action toolbar"
  back: "Back"
  toc: "Table of Contents"
  search: "Search"
  comments: "Comments"
  back_to_top: "Back to Top"
  top: "Top"

# Alert related
alert:
  note: "Note"
  tip: "Tip"
  important: "Important"
  warning: "Warning"
  caution: "Caution"

# TOC related
toc:
  close: "Close"
  title: "Table of Contents"
  navigation: "Table of Contents Navigation"
  empty: "No headings in this article"
  tip: "Click to jump to the section"

# Code block related
code:
  copy: "Copy"
  copied: "Copied"
  selected: "Selected"
  collapse: "Collapse"
  expand: "Expand"
  clickToExpand: "Click to expand and view more"
