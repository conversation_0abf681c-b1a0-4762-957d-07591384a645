<!doctype html>
<html
  lang="{{ site.LanguageCode }}"
  data-theme="{{ site.Params.colorScheme | default "default" }}">
  <head>
    {{ partial "layout/head.html" . }}
    <script>
      // 主题初始化脚本
      (function() {
        const theme = localStorage.getItem('theme') || 'system';
        const colorScheme = localStorage.getItem('colorScheme') || '{{ site.Params.colorScheme | default "shadcn" }}';

        // 设置颜色主题
        document.documentElement.setAttribute('data-theme', colorScheme);

        // 设置明暗模式
        function applyTheme() {
          if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }
        }

        applyTheme();

        // 监听系统主题变化
        if (theme === 'system') {
          window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', applyTheme);
        }
      })();
    </script>
  </head>

  <body class="bg-background text-foreground min-h-screen antialiased">
    <!-- 阅读进度条 -->
    {{ partial "ui/reading-progress.html" . }}

    {{ partial "navigation/header.html" . }}

    <main class="mx-auto max-w-4xl px-4 py-6">
      {{ block "main" . }}{{ end }}
    </main>

    {{ partial "layout/footer.html" . }}

    <!-- 底部 Dock -->
    {{ partial "ui/dock.html" . }}

    <!-- 目录卡片 - 只在有目录的页面显示 -->
    {{- if and .IsPage .TableOfContents }}
      {{ partial "ui/toc-card.html" . }}
    {{- end }}
    {{ partial "features/katex.html" . }}
    {{ partial "features/mermaid.html" . }}

    <!-- 搜索组件 -->
    {{ partial "ui/search-modal.html" . }}
  </body>
</html>
