---
title: "Markdown構文テストドキュメント"
date: 2024-01-15T10:00:00+08:00
draft: false
summary: "これは様々なMarkdown構文を含むテストドキュメントで、プロススタイルの完全性を検証するためのものです。"
categories: ["テスト"]
tags: ["markdown", "プロス", "スタイル"]
---

# 見出し1

これは見出し1の下の段落です。

## 見出し2

これは見出し2の下の段落です。

### 見出し3

これは見出し3の下の段落です。

#### 見出し4

これは見出し4の下の段落です。

##### 見出し5

これは見出し5の下の段落です。

###### 見出し6

これは見出し6の下の段落です。

## 段落とテキスト書式

これは通常の段落です。**太字テキスト**、*斜体テキスト*、***太字斜体テキスト***、~~取り消し線~~、`インラインコード`、[リンクテキスト](https://example.com)を含むことができます。

これは段落間のスペースをテストするための別の段落です。

## 引用ブロック

> これはシンプルな引用ブロックです。
> 
> 引用ブロックは複数の段落を含むことができます。

> これはネストした引用の例です：
> 
> > これはネストした引用の内容です。
> > 
> > 複数レベルのネストが可能です。

## リスト

### 順序なしリスト

- 最初の項目
- 2番目の項目
  - ネストした項目1
  - ネストした項目2
    - さらに深いネストした項目
- 3番目の項目

### 順序付きリスト

1. 最初の項目
2. 2番目の項目
   1. ネストした順序付き項目1
   2. ネストした順序付き項目2
      1. さらに深いネストした項目
3. 3番目の項目

### タスクリスト（チェックボックス）

- [x] 完了したタスク
- [ ] 未完了のタスク
- [x] 別の完了したタスク
- [ ] ネストしたタスクリスト
  - [x] サブタスク1（完了）
  - [ ] サブタスク2（未完了）
  - [x] サブタスク3（完了）

### 定義リスト

用語1
: これは用語1の定義です。

用語2
: これは用語2の定義です。
: 用語は複数の定義を持つことができます。

## コード

### インラインコード

これは`console.log('Hello World')`を含む段落です。

### コードブロック

```javascript
function greet(name) {
  console.log(`こんにちは、${name}！`);
}

greet('世界');
```

```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
```

```css
.prose {
  max-width: none;
  color: var(--tw-prose-body);
}

.prose h1 {
  font-size: 2.25rem;
  font-weight: 700;
}
```

## テーブル

| 左揃え | 中央揃え | 右揃え |
|:-------|:--------:|-------:|
| 内容1  | 内容2    | 内容3  |
| 長い内容 | 中程度   | 短い   |
| データA | データB  | データC |

## 水平線

---

## 画像

![サンプル画像](/images/basic-image.jpg "サンプル画像")

## リンク

これは[通常のリンク](https://example.com)です。

これは[タイトル付きのリンク](https://example.com "リンクタイトル")です。

これは参照スタイルのリンクです：[参照リンク][1]

[1]: https://example.com "参照リンクタイトル"

## 脚注

これは脚注を含む段落です[^1]。

これは別の脚注です[^note]。

[^1]: これは最初の脚注の内容です。

[^note]: これは名前付き脚注の内容です。

## ハイライトテキスト

これは==ハイライトテキスト==を含む段落です。

## 上付き文字と下付き文字

H~2~Oは水の化学式です。

E = mc^2^はアインシュタインの質量エネルギー等価式です。

## キーボードキー

テキストをコピーするには<kbd>Ctrl</kbd> + <kbd>C</kbd>を押してください。

## 略語

HTMLは*ハイパーテキストマークアップ言語*の略語です。

*[HTML]: ハイパーテキストマークアップ言語

## 数式（KaTeXがサポートされている場合）

インライン数式：$E = mc^2$

ブロック数式：

$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

## 警告（サポートされている場合）

> [!NOTE]
> これは注意事項です。

> [!TIP]
> これはヒントです。

> [!IMPORTANT]
> これは重要な情報です。

> [!WARNING]
> これは警告です。

> [!CAUTION]
> これは注意事項です。

## 詳細（サポートされている場合）

<details>
<summary>詳細を展開するにはクリック</summary>

これは折りたたまれた詳細な内容です。

ここには任意のMarkdown構文を含めることができます：

- リスト項目
- **太字テキスト**
- `コード`

</details>

## 混合コンテンツテスト

この段落には複数の形式が含まれています：**太字**、*斜体*、`コード`、[リンク](https://example.com)、~~取り消し線~~、==ハイライト==。

### 複雑なリスト

1. **太字**テキストを含む最初の項目
   - `コード`を含むネストした項目
   - [リンク](https://example.com)を含む別のネストした項目
2. *斜体*テキストを含む2番目の項目
   1. 順序付きネストした項目
   2. 別の順序付きネストした項目
3. ~~取り消し線~~テキストを含む3番目の項目

### 複雑なテーブル

| 機能 | ステータス | 説明 |
|------|:----------:|----|
| **太字** | ✅ | 太字テキストをサポート |
| *斜体* | ✅ | 斜体をサポート |
| `コード` | ✅ | インラインコードをサポート |
| [リンク](https://example.com) | ✅ | リンクをサポート |
| ~~取り消し線~~ | ❌ | テストが必要 |

このテストドキュメントは、最も一般的なMarkdown構文をカバーしており、プロススタイルの完全性と美観を検証するために使用できます。
