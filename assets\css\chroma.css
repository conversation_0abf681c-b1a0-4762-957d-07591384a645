
/* Github light */
:root:not(.dark) {
  /* 关键字 - 红色系 */
  --chroma-k: #cf222e;
  --chroma-kc: #0550ae;
  --chroma-kp: #0550ae;

  /* 名称 - 蓝色/紫色系 */
  --chroma-n: inherit;
  --chroma-na: #1f2328;
  --chroma-nc: #1f2328;
  --chroma-no: #0550ae;
  --chroma-nd: #0550ae;
  --chroma-ni: #6639ba;
  --chroma-nn: #24292e;
  --chroma-nx: #1f2328;
  --chroma-nt: #0550ae;
  --chroma-nb: #6639ba;
  --chroma-bp: #6a737d;
  --chroma-nv: #953800;
  --chroma-nf: #6639ba;

  /* 字面量 - 蓝色系 */
  --chroma-s: #0a3069;
  --chroma-ss: #032f62;
  --chroma-m: #0550ae;

  /* 操作符和标点 */
  --chroma-o: #0550ae;
  --chroma-p: #1f2328;

  /* 注释 - 灰色 */
  --chroma-c: #57606a;

  /* 错误和高亮 */
  --chroma-err: #f6f8fa;
  --chroma-err-bg: #82071e;
  --chroma-hl: #e5e5e5;
  --chroma-ln: #7f7f7f;

  /* 差异显示 */
  --chroma-gd: #82071e;
  --chroma-gd-bg: #ffebe9;
  --chroma-gi: #116329;
  --chroma-gi-bg: #dafbe1;
  --chroma-go: #1f2328;
}

/* GitHub Dark */
.dark {
  /* 关键字 - 红色/橙色系 */
  --chroma-k: #ff7b72;
  --chroma-kc: #79c0ff;
  --chroma-kp: #79c0ff;

  /* 名称 - 蓝色/紫色/橙色系 */
  --chroma-n: inherit;
  --chroma-nc: #f0883e;
  --chroma-no: #79c0ff;
  --chroma-nd: #d2a8ff;
  --chroma-ni: #ffa657;
  --chroma-ne: #f0883e;
  --chroma-nl: #79c0ff;
  --chroma-nn: #ff7b72;
  --chroma-nx: inherit;
  --chroma-py: #79c0ff;
  --chroma-nt: #7ee787;
  --chroma-nb: #d2a8ff;
  --chroma-bp: #8b949e;
  --chroma-nv: #79c0ff;
  --chroma-nf: #d2a8ff;

  /* 字面量 - 蓝色系 */
  --chroma-l: #a5d6ff;
  --chroma-ld: #79c0ff;
  --chroma-s: #a5d6ff;
  --chroma-sa: #79c0ff;
  --chroma-dl: #79c0ff;
  --chroma-se: #79c0ff;
  --chroma-sh: #79c0ff;
  --chroma-sr: #79c0ff;
  --chroma-ss: #a5d6ff;
  --chroma-m: #a5d6ff;

  /* 操作符和标点 */
  --chroma-o: #ff7b72;
  --chroma-p: inherit;

  /* 注释 - 灰色 */
  --chroma-c: #8b949e;
  --chroma-cs: #8b949e;

  /* 错误和高亮 */
  --chroma-err: #f85149;
  --chroma-hl: #6e7681;
  --chroma-ln: #6e7681;

  /* 差异显示 */
  --chroma-gd: #ffa198;
  --chroma-gd-bg: #490202;
  --chroma-gh: #79c0ff;
  --chroma-gi: #56d364;
  --chroma-gi-bg: #0f5323;
  --chroma-go: #8b949e;
  --chroma-gp: #8b949e;
  --chroma-gu: #79c0ff;
  --chroma-gt: #ff7b72;
  --chroma-w: #6e7681;
}

/* Chroma 样式应用 */
.chroma { 
  color: var(--chroma-color, inherit);
}

.chroma .err { 
  color: var(--chroma-err); 
  background-color: var(--chroma-err-bg, transparent); 
}

.chroma .lnlinks { 
  outline: none; 
  text-decoration: none; 
  color: inherit; 
}

.chroma .lntd { 
  vertical-align: top; 
  padding: 0; 
  margin: 0; 
  border: 0; 
}

.chroma .lntable { 
  border-spacing: 0; 
  padding: 0; 
  margin: 0; 
  border: 0; 
}

.chroma .hl { 
  background-color: var(--chroma-hl); 
}

.chroma .lnt, .chroma .ln { 
  white-space: pre; 
  user-select: none; 
  margin-right: 0.4em; 
  padding: 0 0.4em 0 0.4em; 
  color: var(--chroma-ln); 
}

.chroma .line { 
  display: flex; 
}

/* 关键字 */
.chroma .k, .chroma .kc, .chroma .kd, .chroma .kn, .chroma .kr, .chroma .kt { 
  color: var(--chroma-k); 
}

.chroma .kp { 
  color: var(--chroma-kp, var(--chroma-k)); 
}

/* 名称 */
.chroma .n { 
  color: var(--chroma-n); 
}

.chroma .na { 
  color: var(--chroma-na, var(--chroma-n)); 
}

.chroma .nc { 
  color: var(--chroma-nc, var(--chroma-n)); 
  font-weight: bold; 
}

.chroma .no { 
  color: var(--chroma-no, var(--chroma-n)); 
  font-weight: bold; 
}

.chroma .nd { 
  color: var(--chroma-nd, var(--chroma-n)); 
  font-weight: bold; 
}

.chroma .ni { 
  color: var(--chroma-ni, var(--chroma-n)); 
}

.chroma .ne { 
  color: var(--chroma-ne, var(--chroma-n)); 
  font-weight: bold; 
}

.chroma .nl { 
  color: var(--chroma-nl, var(--chroma-n)); 
  font-weight: bold; 
}

.chroma .nn { 
  color: var(--chroma-nn, var(--chroma-n)); 
}

.chroma .nx { 
  color: var(--chroma-nx, var(--chroma-n)); 
}

.chroma .py { 
  color: var(--chroma-py, var(--chroma-n)); 
}

.chroma .nt { 
  color: var(--chroma-nt, var(--chroma-n)); 
}

.chroma .nb { 
  color: var(--chroma-nb, var(--chroma-n)); 
}

.chroma .bp { 
  color: var(--chroma-bp, var(--chroma-n)); 
}

.chroma .nv, .chroma .vc, .chroma .vg, .chroma .vi, .chroma .vm { 
  color: var(--chroma-nv, var(--chroma-n)); 
}

.chroma .nf, .chroma .fm { 
  color: var(--chroma-nf, var(--chroma-n)); 
  font-weight: bold; 
}

/* 字面量 */
.chroma .l { 
  color: var(--chroma-l, inherit); 
}

.chroma .ld { 
  color: var(--chroma-ld, var(--chroma-l)); 
}

.chroma .s, .chroma .sb, .chroma .sc, .chroma .sd, .chroma .s2, .chroma .si, .chroma .sx, .chroma .s1 { 
  color: var(--chroma-s); 
}

.chroma .sa, .chroma .dl, .chroma .se, .chroma .sh, .chroma .sr { 
  color: var(--chroma-sa, var(--chroma-s)); 
}

.chroma .ss { 
  color: var(--chroma-ss, var(--chroma-s)); 
}

.chroma .m, .chroma .mb, .chroma .mf, .chroma .mh, .chroma .mi, .chroma .il, .chroma .mo { 
  color: var(--chroma-m); 
}

/* 操作符 */
.chroma .o, .chroma .ow { 
  color: var(--chroma-o); 
  font-weight: bold; 
}

/* 标点 */
.chroma .p { 
  color: var(--chroma-p, inherit); 
}

/* 注释 */
.chroma .c, .chroma .ch, .chroma .cm, .chroma .c1 { 
  color: var(--chroma-c); 
  font-style: italic; 
}

.chroma .cs, .chroma .cp, .chroma .cpf { 
  color: var(--chroma-cs, var(--chroma-c)); 
  font-weight: bold; 
  font-style: italic; 
}

/* 通用 */
.chroma .gd { 
  color: var(--chroma-gd); 
  background-color: var(--chroma-gd-bg, transparent); 
}

.chroma .ge { 
  font-style: italic; 
}

.chroma .gr { 
  color: var(--chroma-gd); 
}

.chroma .gh { 
  color: var(--chroma-gh, inherit); 
  font-weight: bold; 
}

.chroma .gi { 
  color: var(--chroma-gi); 
  background-color: var(--chroma-gi-bg, transparent); 
}

.chroma .go { 
  color: var(--chroma-go, inherit); 
}

.chroma .gp { 
  color: var(--chroma-gp, inherit); 
}

.chroma .gs { 
  font-weight: bold; 
}

.chroma .gu { 
  color: var(--chroma-gu, inherit); 
}

.chroma .gt { 
  color: var(--chroma-gt, inherit); 
}

.chroma .gl { 
  text-decoration: underline; 
}

.chroma .w { 
  color: var(--chroma-w, inherit); 
}
