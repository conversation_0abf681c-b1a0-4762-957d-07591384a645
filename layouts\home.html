{{ define "main" }}
  <div class="mx-auto max-w-4xl">
    <!-- 作者信息区域 -->
    {{ if site.Params.author }}
      <section class="author-section mb-12">
        <div class="bg-card border-border rounded-xl border p-8 shadow-sm">
          <div
            class="flex flex-col items-center gap-6 text-center md:flex-row md:items-start md:text-left">
            <!-- 头像 -->
            <div class="flex-shrink-0">
              {{ if site.Params.author.avatar }}
                <img
                  src="{{ site.Params.author.avatar }}"
                  alt="{{ site.Params.author.name | default site.Title }}"
                  class="border-primary/20 h-24 w-24 rounded-full border-4 object-cover shadow-lg" />
              {{ else }}
                <!-- 默认头像：使用作者姓名首字母 -->
                <div
                  class="bg-primary text-primary-foreground flex h-24 w-24 items-center justify-center rounded-full text-2xl font-bold shadow-lg">
                  {{ substr (site.Params.author.name | default site.Title) 0 1 | upper }}
                </div>
              {{ end }}
            </div>

            <!-- 作者信息 -->
            <div class="min-w-0 flex-1">
              <h1 class="text-foreground mb-2 text-3xl font-bold">
                {{ site.Params.author.name | default site.Title }}
              </h1>

              {{ if site.Params.author.title }}
                <p class="text-muted-foreground mb-3 text-lg">
                  {{ site.Params.author.title }}
                </p>
              {{ end }}

              {{ if site.Params.author.description }}
                <p class="text-foreground mb-4 leading-relaxed">
                  {{ site.Params.author.description }}
                </p>
              {{ end }}


              <!-- 社交链接 -->
              {{ if site.Params.author.social }}
                <div
                  class="flex flex-wrap justify-center gap-3 md:justify-start">
                  {{ range site.Params.author.social }}
                    <a
                      href="{{ .url }}"
                      target="_blank"
                      rel="noopener noreferrer"
                      class="bg-muted text-muted-foreground hover:text-primary hover:bg-primary/10 focus:ring-primary/20 inline-flex items-center gap-2 rounded-lg px-3 py-2 transition-all duration-300 ease-out hover:-translate-y-0.5 hover:scale-105 focus:ring-2 focus:outline-none"
                      title="{{ .name }}">
                      {{ if .icon }}
                        {{ partial "features/icon.html" (dict "name" .icon "size" "sm" "ariaLabel" .name) }}
                      {{ end }}
                      <span class="text-sm font-medium">{{ .name }}</span>
                    </a>
                  {{ end }}
                </div>
              {{ end }}
            </div>
          </div>
        </div>
      </section>
    {{ end }}


    <!-- 最近文章区域 -->
    {{ $recentPostsCount := site.Params.home.recentPostsCount | default 5 }}
    {{ $posts := where site.RegularPages "Section" "posts" | first $recentPostsCount }}

    {{ partial "content/post-list.html" (dict
      "posts" $posts
      "title" (i18n "home.recent_posts" | default "最近文章")
      "showTitle" true
      "showViewAll" true
      "viewAllUrl" ("/posts/" | relLangURL)
      )
    }}


    <!-- 页面内容 -->
    {{ if .Content }}
      <section class="page-content mt-12">
        <div class="prose max-w-none">
          {{ .Content }}
        </div>
      </section>
    {{ end }}

  </div>
{{ end }}
