# 中文语言文件
# 导航相关
nav:
  menu: "菜单"
  home: "首页"
  posts: "文章"
  tags: "标签"
  categories: "分类"
  about: "关于"
  contact: "联系"
  archive: "归档"
  archives: "归档"
  search: "搜索"
  breadcrumb: "面包屑导航"
  pagination: "分页导航"
  prev: "上一页"
  next: "下一页"
  prev_page: "上一页"
  next_page: "下一页"
  page_info: "第 {{ .current }} 页，共 {{ .total }} 页"
  total_pages: "共 {{ .count }} 页"
  back_home: "返回首页"
  post_navigation: "文章导航"

# 主题相关
theme:
  toggle: "切换主题风格"
  light: "亮色"
  dark: "暗色"
  system: "跟随系统"
  auto: "自动"

# 页脚相关
footer:
  copyright: "版权所有"
  all_rights_reserved: "保留所有权利"
  about_us: "关于我们"
  contact_us: "联系方式"
  privacy_policy: "隐私政策"
  terms_of_service: "服务条款"
  rss_feed: "RSS 订阅"

# 首页相关
home:
  recent_posts: "最近文章"
  showing_posts: "显示最新"
  posts: "篇文章"
  view_all_posts: "查看所有文章"

# 文章相关
post:
  read_more: "阅读更多"
  published_on: "发布于"
  updated_on: "更新于"
  tags: "标签"
  categories: "分类"
  share: "分享"
  previous: "上一篇"
  next: "下一篇"
  related_posts: "相关文章"
  reading_time: "阅读时间"
  no_posts: "暂无文章"
  no_posts_desc: "还没有发布任何文章，请稍后再来查看。"
  list_desc: "这里是所有已发布的文章，按时间倒序排列。"
  total_posts: "共 {{ .count }} 篇文章"
  author: "作者"
  words: "字"
  no_previous: "没有上一篇"
  no_next: "没有下一篇"
  no_previous_desc: "这是最新的文章"
  no_next_desc: "这是最早的文章"
  view_more_posts: "查看更多文章"
  reading_time_unit: "分钟"

# 分页相关
pagination:
  previous: "上一页"
  next: "下一页"
  page: "第"
  of: "页，共"
  pages: "页"

# 搜索相关
search:
  placeholder: "搜索文章..."
  clear: "清空"
  close: "关闭"
  loading: "搜索中..."
  empty_title: "开始搜索"
  empty_description: "输入关键词搜索文章内容"
  no_results_title: "未找到结果"
  no_results_description: "尝试使用不同的关键词"
  results_count: "找到 %d 个结果"
  match_score: "匹配度"
  hint_navigate: "导航"
  hint_select: "选择"
  hint_close: "关闭"
  hint_shortcut: "快捷键"

# 表单相关
form:
  name: "姓名"
  email: "邮箱"
  message: "消息"
  subject: "主题"
  send: "发送"
  required: "必填"

# 时间相关
time:
  minute: "分钟"
  minutes: "分钟"
  hour: "小时"
  hours: "小时"
  day: "天"
  days: "天"
  week: "周"
  weeks: "周"
  month: "月"
  months: "月"
  year: "年"
  years: "年"
  ago: "前"
  date_format: "2006 年 01 月 02 日"
  date_format_short: "01 月 02 日"
  month_format: "2006 年 01 月"

# 归档相关
archives:
  desc: "按时间顺序浏览所有文章，发现您感兴趣的内容。"
  posts_count: "篇文章"
  no_posts: "暂无文章"
  no_posts_desc: "还没有发布任何文章，请稍后再来查看。"
  total_posts: "共 {{ .count }} 篇文章"
  timeline: "时间轴视图"

# 分类法相关
taxonomy:
  total_tags: "共 {{ .count }} 个标签"
  total_categories: "共 {{ .count }} 个分类"
  total_terms: "共 {{ .count }} 个项目"
  tags_desc: "浏览所有标签，发现感兴趣的内容。"
  categories_desc: "浏览所有分类，发现感兴趣的内容。"
  desc: "浏览所有分类，发现感兴趣的内容。"
  no_terms: "暂无分类"
  no_terms_desc: "还没有创建任何分类，请稍后再来查看。"

# 分类法术语页面
term:
  tag_desc: "标签 \"{{ .tag }}\" 下的所有文章"
  category_desc: "分类 \"{{ .category }}\" 下的所有文章"
  desc: "\"{{ .term }}\" 下的所有文章"
  total_posts: "共 {{ .count }} 篇文章"
  date_range: "按时间排序"
  no_posts: "暂无文章"
  no_posts_tag: "该标签下还没有文章。"
  no_posts_category: "该分类下还没有文章。"
  no_posts_desc: "该分类下还没有文章。"

# 通用
common:
  loading: "加载中..."
  error: "错误"
  success: "成功"
  close: "关闭"
  open: "打开"
  save: "保存"
  cancel: "取消"
  confirm: "确认"
  delete: "删除"
  edit: "编辑"
  view: "查看"
  back: "返回"
  more: "更多"
  language: "语言"
  site_logo: "站点 Logo"

# 语言切换
language:
  toggle: "切换语言"

# 许可证相关
license:
  title: "版权声明"
  author: "作者"
  link: "链接"
  type: "许可证"
  cc_by_nc_sa_desc: "本作品采用知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议进行许可。转载请注明出处，非商业性使用，并保持相同的许可方式。"
  cc_by_desc: "本作品采用知识共享署名 4.0 国际许可协议进行许可。转载请注明出处。"

# 评论相关
comments:
  title: "评论"
  not_configured: "评论系统未配置"
  not_configured_desc: "请在站点配置中启用评论系统"
  giscus_not_configured: "Giscus 评论系统未配置"
  disqus_not_configured: "Disqus 评论系统未配置"
  disqus_noscript: "请启用 JavaScript 来查看评论。"
  utterances_not_configured: "Utterances 评论系统未配置"
  waline_not_configured: "Waline 评论系统未配置"

# 底部 Dock
dock:
  aria_label: "快捷操作工具栏"
  back: "返回"
  toc: "目录"
  search: "搜索"
  comments: "评论"
  back_to_top: "返回顶部"
  top: "顶部"

# 提醒框相关
alert:
  note: "注意"
  tip: "提示"
  important: "重要"
  warning: "警告"
  caution: "小心"

# 目录相关
toc:
  close: "关闭"
  title: "目录"
  navigation: "文章目录导航"
  empty: "此文章没有标题"
  tip: "点击标题快速跳转"

# 代码块相关
code:
  copy: "复制"
  copied: "已复制"
  selected: "已选择"
  collapse: "折叠"
  expand: "展开"
  clickToExpand: "点击展开查看更多"
