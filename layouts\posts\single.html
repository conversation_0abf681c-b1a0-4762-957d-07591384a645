{{ define "main" }}
  <!-- 面包屑导航 -->
  {{ partial "navigation/breadcrumb.html" . }}


  <!-- 文章封面 -->
  {{ if .Params.cover }}
    {{/* 使用共享的图片处理器 */}}
    {{ partial "content/cover-image-processor.html" . }}
    {{ $coverImage := .Scratch.Get "coverImage" }}
    {{ $coverProcessed := .Scratch.Get "coverProcessed" }}
    {{ $gradients := .Scratch.Get "gradients" }}
    {{ $colorIndex := .Scratch.Get "colorIndex" }}

    {{ if $coverImage }}
      <div class="mb-8 overflow-hidden rounded-2xl">
        <img
          src="{{ $coverImage }}"
          alt="{{ .Title }}"
          class="h-64 w-full object-cover md:h-80"
          loading="eager"
          onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
        <!-- 图片加载失败时的占位图 -->
        <div class="{{ index $gradients $colorIndex }} relative h-64 md:h-80 w-full bg-gradient-to-br" style="display: none;">
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="flex h-24 w-24 items-center justify-center rounded-full border border-white/30 bg-white/20 backdrop-blur-sm">
              <span class="text-3xl font-bold text-white">{{ substr .Title 0 1 | upper }}</span>
            </div>
          </div>
          <!-- 装饰元素 -->
          <div class="absolute top-8 right-8 h-20 w-20 rounded-full bg-white/15"></div>
          <div class="absolute bottom-8 left-8 h-16 w-16 rounded-full bg-white/20"></div>
        </div>
      </div>
    {{ end }}
  {{ end }}


  <!-- 文章头部信息 -->
  <header class="mb-8">
    {{ partial "content/post-meta.html" . }}
  </header>

  <!-- 文章内容 -->
  <article class="prose prose-neutral dark:prose-invert mb-12 max-w-none">
    {{ .Content }}
  </article>

  <!-- 许可证信息 -->
  {{ partial "content/post-license.html" . }}


  <!-- 上一篇/下一篇导航 -->
  {{ partial "content/post-navigation.html" . }}


  <!-- 相关文章 -->
  {{ partial "content/related-posts.html" . }}


  <!-- 评论组件 -->
  {{ partial "features/comments.html" . }}

{{ end }}
