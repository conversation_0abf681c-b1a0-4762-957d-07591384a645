:root {
  --color-primary: oklch(0.55 0.15 252.5);
  --color-primary-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.97 0 0);
  --color-secondary-foreground: oklch(0.205 0 0);
  --color-accent: oklch(0.97 0 0);
  --color-accent-foreground: oklch(0.205 0 0);
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.145 0 0);
  --color-muted: oklch(0.97 0 0);
  --color-muted-foreground: oklch(0.556 0 0);
  --color-border: oklch(0.922 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.145 0 0);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.145 0 0);


  --color-note: oklch(0.61 0.15 252.5);
  --color-tip: oklch(0.61 0.15 162.48);
  --color-important: oklch(0.61 0.15 39.04);
  --color-warning: oklch(0.61 0.15 85.87);
  --color-caution: oklch(0.61 0.15 12.57);
}

.dark {
  --color-primary: oklch(0.65 0.15 252.5);
  --color-primary-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.269 0 0);
  --color-secondary-foreground: oklch(0.985 0 0);
  --color-accent: oklch(0.371 0 0);
  --color-accent-foreground: oklch(0.985 0 0);
  --color-background: oklch(0.145 0 0);
  --color-foreground: oklch(0.985 0 0);
  --color-muted: oklch(0.269 0 0);
  --color-muted-foreground: oklch(0.708 0 0);
  --color-border: oklch(1 0 0 / 10%);
  --color-card: oklch(0.205 0 0);
  --color-card-foreground: oklch(0.985 0 0);
  --color-popover: oklch(0.269 0 0);
  --color-popover-foreground: oklch(0.985 0 0);

  --color-note: oklch(0.67 0.15 252.5);
  --color-tip: oklch(0.67 0.15 162.48);
  --color-important: oklch(0.67 0.15 39.04);
  --color-warning: oklch(0.67 0.15 85.87);
  --color-caution: oklch(0.67 0.15 12.57);
}

[data-theme="claude"] {
  --color-primary: oklch(0.62 0.14 39.04);
  --color-primary-foreground: oklch(1 0 0);
  --color-secondary: oklch(0.92 0.01 92.99);
  --color-secondary: oklch(0.92 0.01 92.99);
  --color-secondary-foreground: oklch(0.43 0.02 98.6);
  --color-accent: oklch(0.92 0.01 92.99);
  --color-accent-foreground: oklch(0.27 0.02 98.94);
  --color-background: oklch(0.98 0.01 95.1);
  --color-foreground: oklch(0.34 0.03 95.72);
  --color-muted: oklch(0.93 0.02 90.24);
  --color-muted-foreground: oklch(0.61 0.01 97.42);
  --color-border: oklch(0.88 0.01 97.36);
  --color-card: oklch(0.98 0.01 95.1);
  --color-card-foreground: oklch(0.19 0 106.59);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.27 0.02 98.94);

  --color-note: oklch(0.58 0.13 240.0); 
  --color-tip: oklch(0.60 0.12 150.0); 
  --color-important: oklch(0.62 0.14 39.04); 
  --color-warning: oklch(0.63 0.14 70.0);
  --color-caution: oklch(0.58 0.15 25.0);
}

[data-theme="claude"].dark {
  --color-primary: oklch(0.67 0.13 38.76);
  --color-primary-foreground: oklch(1 0 0);
  --color-secondary: oklch(0.98 0.01 95.1);
  --color-secondary-foreground: oklch(0.31 0 106.6);
  --color-accent: oklch(0.21 0.01 95.42);
  --color-accent-foreground: oklch(0.97 0.01 98.88);
  --color-background: oklch(0.27 0 106.64);
  --color-foreground: oklch(0.81 0.01 93.01);
  --color-muted: oklch(0.22 0 106.71);
  --color-muted-foreground: oklch(0.77 0.02 99.07);
  --color-border: oklch(0.36 0.01 106.89);
  --color-card: oklch(0.27 0 106.64);
  --color-card-foreground: oklch(0.98 0.01 95.1);
  --color-popover: oklch(0.31 0 106.6);
  --color-popover-foreground: oklch(0.92 0 106.48);

  --color-note: oklch(0.65 0.12 235.0); 
  --color-tip: oklch(0.68 0.11 145.0); 
  --color-important: oklch(0.67 0.13 38.76);
  --color-warning: oklch(0.70 0.13 68.0);
  --color-caution: oklch(0.65 0.14 20.0);
}


[data-theme="bumblebee"] {
  --color-primary: oklch(0.85 0.199 91.936);
  --color-primary-foreground: oklch(0.42 0.095 57.708);
  --color-secondary: oklch(0.75 0.183 55.934);
  --color-secondary-foreground: oklch(0.40 0.123 38.172);
  --color-accent: oklch(0 0 0);
  --color-accent-foreground: oklch(1 0 0);
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.20 0 0);
  --color-muted: oklch(0.97 0 0);
  --color-muted-foreground: oklch(0.37 0.01 67.558);
  --color-border: oklch(0.92 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.20 0 0);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.20 0 0);

  --color-note: oklch(0.74 0.16 232.661);
  --color-tip: oklch(0.76 0.177 163.223);
  --color-important: oklch(0.85 0.199 91.936);
  --color-warning: oklch(0.82 0.189 84.429);
  --color-caution: oklch(0.70 0.191 22.216);
}

[data-theme="bumblebee"].dark {
  --color-primary: oklch(0.70 0.16 91.936);
  --color-primary-foreground: oklch(0.95 0 0);
  --color-secondary: oklch(0.45 0.15 55.934);
  --color-secondary-foreground: oklch(0.95 0 0);
  --color-accent: oklch(0.15 0 0);
  --color-accent-foreground: oklch(0.95 0 0);
  --color-background: oklch(0.12 0 0);
  --color-foreground: oklch(0.92 0.003 48.717);
  --color-muted: oklch(0.18 0 0);
  --color-muted-foreground: oklch(0.65 0.01 67.558);
  --color-border: oklch(0.25 0 0);
  --color-card: oklch(0.15 0 0);
  --color-card-foreground: oklch(0.92 0.003 48.717);
  --color-popover: oklch(0.18 0 0);
  --color-popover-foreground: oklch(0.92 0.003 48.717);

  --color-note: oklch(0.65 0.14 235.0);
  --color-tip: oklch(0.68 0.15 160.0);
  --color-important: oklch(0.70 0.16 91.936);
  --color-warning: oklch(0.75 0.16 82.0);
  --color-caution: oklch(0.65 0.16 25.0);
}


[data-theme="emerald"] {
  --color-primary: oklch(0.767 0.135 153.45);
  --color-primary-foreground: oklch(0.334 0.04 162.24);
  --color-secondary: oklch(0.613 0.202 261.294);
  --color-secondary-foreground: oklch(1 0 0);
  --color-accent: oklch(0.728 0.149 33.2);
  --color-accent-foreground: oklch(0 0 0);
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.355 0.032 262.988);
  --color-muted: oklch(0.93 0 0);
  --color-muted-foreground: oklch(0.355 0.032 262.988);
  --color-border: oklch(0.86 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.355 0.032 262.988);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.355 0.032 262.988);

  --color-note: oklch(0.721 0.191 231.6);
  --color-tip: oklch(0.648 0.15 160);
  --color-important: oklch(0.767 0.135 153.45);
  --color-warning: oklch(0.847 0.199 83.87);
  --color-caution: oklch(0.718 0.221 22.18);
}

[data-theme="emerald"].dark {
  --color-primary: oklch(0.65 0.12 153.45);
  --color-primary-foreground: oklch(0.95 0 0);
  --color-secondary: oklch(0.45 0.16 261.294);
  --color-secondary-foreground: oklch(0.95 0 0);
  --color-accent: oklch(0.55 0.12 33.2);
  --color-accent-foreground: oklch(0.95 0 0);
  --color-background: oklch(0.12 0 0);
  --color-foreground: oklch(0.985 0.001 247.838);
  --color-muted: oklch(0.18 0 0);
  --color-muted-foreground: oklch(0.65 0.02 262.988);
  --color-border: oklch(0.25 0 0);
  --color-card: oklch(0.15 0 0);
  --color-card-foreground: oklch(0.985 0.001 247.838);
  --color-popover: oklch(0.18 0 0);
  --color-popover-foreground: oklch(0.985 0.001 247.838);

  --color-note: oklch(0.65 0.16 235.0);
  --color-tip: oklch(0.70 0.13 158.0);
  --color-important: oklch(0.65 0.12 153.45);
  --color-warning: oklch(0.75 0.16 82.0);
  --color-caution: oklch(0.65 0.18 25.0);
}


[data-theme="nord"] {
  --color-primary: oklch(0.594 0.077 254.027);
  --color-primary-foreground: oklch(0.119 0.015 254.027);
  --color-secondary: oklch(0.697 0.059 248.687);
  --color-secondary-foreground: oklch(0.139 0.011 248.687);
  --color-accent: oklch(0.775 0.062 217.469);
  --color-accent-foreground: oklch(0.155 0.012 217.469);
  --color-background: oklch(0.951 0.007 260.731);
  --color-foreground: oklch(0.324 0.022 264.182);
  --color-muted: oklch(0.933 0.01 261.788);
  --color-muted-foreground: oklch(0.452 0.035 264.131);
  --color-border: oklch(0.899 0.016 262.749);
  --color-card: oklch(0.951 0.007 260.731);
  --color-card-foreground: oklch(0.324 0.022 264.182);
  --color-popover: oklch(0.951 0.007 260.731);
  --color-popover-foreground: oklch(0.324 0.022 264.182);

  --color-note: oklch(0.692 0.062 332.664);
  --color-tip: oklch(0.768 0.074 131.063);
  --color-important: oklch(0.594 0.077 254.027);
  --color-warning: oklch(0.855 0.089 84.093);
  --color-caution: oklch(0.606 0.12 15.341);
}

[data-theme="nord"].dark {
  --color-primary: oklch(0.55 0.07 254.027);
  --color-primary-foreground: oklch(0.95 0 0);
  --color-secondary: oklch(0.55 0.05 248.687);
  --color-secondary-foreground: oklch(0.95 0 0);
  --color-accent: oklch(0.65 0.05 217.469);
  --color-accent-foreground: oklch(0.95 0 0);
  --color-background: oklch(0.18 0.015 264.182);
  --color-foreground: oklch(0.899 0.016 262.749);
  --color-muted: oklch(0.25 0.02 264.131);
  --color-muted-foreground: oklch(0.70 0.035 264.131);
  --color-border: oklch(0.35 0.025 264.182);
  --color-card: oklch(0.22 0.018 264.182);
  --color-card-foreground: oklch(0.899 0.016 262.749);
  --color-popover: oklch(0.25 0.02 264.131);
  --color-popover-foreground: oklch(0.899 0.016 262.749);

  --color-note: oklch(0.65 0.055 330.0);
  --color-tip: oklch(0.70 0.065 128.0);
  --color-important: oklch(0.55 0.07 254.027);
  --color-warning: oklch(0.75 0.08 82.0);
  --color-caution: oklch(0.60 0.11 18.0);
}


[data-theme="sunset"] {
  --color-primary: oklch(0.747 0.158 39.947);
  --color-primary-foreground: oklch(0.149 0.031 39.947);
  --color-secondary: oklch(0.725 0.177 2.72);
  --color-secondary-foreground: oklch(0.145 0.035 2.72);
  --color-accent: oklch(0.713 0.166 299.844);
  --color-accent-foreground: oklch(0.143 0.033 299.844);
  --color-background: oklch(0.98 0.01 40);
  --color-foreground: oklch(0.22 0.019 237.69);
  --color-muted: oklch(0.94 0.015 40);
  --color-muted-foreground: oklch(0.45 0.025 237.69);
  --color-border: oklch(0.88 0.02 40);
  --color-card: oklch(0.98 0.01 40);
  --color-card-foreground: oklch(0.22 0.019 237.69);
  --color-popover: oklch(0.99 0.005 40);
  --color-popover-foreground: oklch(0.22 0.019 237.69);

  --color-note: oklch(0.65 0.15 240);
  --color-tip: oklch(0.70 0.14 160);
  --color-important: oklch(0.747 0.158 39.947);
  --color-warning: oklch(0.75 0.16 85);
  --color-caution: oklch(0.65 0.18 15);
}

[data-theme="sunset"].dark {
  --color-primary: oklch(0.747 0.158 39.947);
  --color-primary-foreground: oklch(0.149 0.031 39.947);
  --color-secondary: oklch(0.725 0.177 2.72);
  --color-secondary-foreground: oklch(0.145 0.035 2.72);
  --color-accent: oklch(0.713 0.166 299.844);
  --color-accent-foreground: oklch(0.143 0.033 299.844);
  --color-background: oklch(0.22 0.019 237.69);
  --color-foreground: oklch(0.774 0.043 245.096);
  --color-muted: oklch(0.26 0.019 237.69);
  --color-muted-foreground: oklch(0.70 0.019 237.69);
  --color-border: oklch(0.35 0.025 237.69);
  --color-card: oklch(0.24 0.019 237.69);
  --color-card-foreground: oklch(0.774 0.043 245.096);
  --color-popover: oklch(0.26 0.019 237.69);
  --color-popover-foreground: oklch(0.774 0.043 245.096);


  --color-note: oklch(0.70 0.14 240);
  --color-tip: oklch(0.75 0.13 160);
  --color-important: oklch(0.747 0.158 39.947);
  --color-warning: oklch(0.80 0.15 85);
  --color-caution: oklch(0.70 0.17 15);
}


[data-theme="abyss"] {
  --color-primary: oklch(0.75 0.20 125);
  --color-primary-foreground: oklch(0.15 0.15 125);
  --color-secondary: oklch(0.70 0.12 298.3);
  --color-secondary-foreground: oklch(0.20 0.08 298.3);
  --color-accent: oklch(0.65 0.15 209);
  --color-accent-foreground: oklch(0.98 0 0);
  --color-background: oklch(0.96 0.02 209);
  --color-foreground: oklch(0.25 0.08 209);
  --color-muted: oklch(0.92 0.03 209);
  --color-muted-foreground: oklch(0.45 0.06 209);
  --color-border: oklch(0.85 0.04 209);
  --color-card: oklch(0.96 0.02 209);
  --color-card-foreground: oklch(0.25 0.08 209);
  --color-popover: oklch(0.98 0.01 209);
  --color-popover-foreground: oklch(0.25 0.08 209);

  --color-note: oklch(0.65 0.15 240);
  --color-tip: oklch(0.75 0.20 125);
  --color-important: oklch(0.75 0.20 125);
  --color-warning: oklch(0.75 0.16 85);
  --color-caution: oklch(0.65 0.18 15);
}

[data-theme="abyss"].dark {
  --color-primary: oklch(0.92 0.2653 125);
  --color-primary-foreground: oklch(0.50 0.2653 125);
  --color-secondary: oklch(0.833 0.0764 298.3);
  --color-secondary-foreground: oklch(0.433 0.0764 298.3);
  --color-accent: oklch(0.43 0 0);
  --color-accent-foreground: oklch(0.98 0 0);
  --color-background: oklch(0.20 0.08 209);
  --color-foreground: oklch(0.90 0.076 70.697);
  --color-muted: oklch(0.30 0.08 209);
  --color-muted-foreground: oklch(0.70 0.06 209);
  --color-border: oklch(0.35 0.08 209);
  --color-card: oklch(0.22 0.08 209);
  --color-card-foreground: oklch(0.90 0.076 70.697);
  --color-popover: oklch(0.30 0.08 209);
  --color-popover-foreground: oklch(0.90 0.076 70.697);

  --color-note: oklch(0.70 0.14 240);
  --color-tip: oklch(0.80 0.22 125);
  --color-important: oklch(0.92 0.2653 125);
  --color-warning: oklch(0.80 0.15 85);
  --color-caution: oklch(0.70 0.17 15);
}

[data-theme="dracula"] {
  --color-primary: oklch(0.70 0.15 346.812);
  --color-primary-foreground: oklch(0.98 0.007 106.545);
  --color-secondary: oklch(0.68 0.12 301.883);
  --color-secondary-foreground: oklch(0.98 0.007 106.545);
  --color-accent: oklch(0.75 0.10 66.558);
  --color-accent-foreground: oklch(0.20 0.024 66.558);
  --color-background: oklch(0.95 0.01 277.508);
  --color-foreground: oklch(0.30 0.022 277.508);
  --color-muted: oklch(0.90 0.015 277.508);
  --color-muted-foreground: oklch(0.50 0.02 277.508);
  --color-border: oklch(0.82 0.02 277.508);
  --color-card: oklch(0.95 0.01 277.508);
  --color-card-foreground: oklch(0.30 0.022 277.508);
  --color-popover: oklch(0.97 0.005 277.508);
  --color-popover-foreground: oklch(0.30 0.022 277.508);

  --color-note: oklch(0.65 0.15 240);
  --color-tip: oklch(0.70 0.14 160);
  --color-important: oklch(0.70 0.15 346.812);
  --color-warning: oklch(0.75 0.16 85);
  --color-caution: oklch(0.65 0.18 15);
}

[data-theme="dracula"].dark {
  --color-primary: oklch(0.755 0.183 346.812);
  --color-primary-foreground: oklch(0.151 0.036 346.812);
  --color-secondary: oklch(0.742 0.148 301.883);
  --color-secondary-foreground: oklch(0.148 0.029 301.883);
  --color-accent: oklch(0.834 0.124 66.558);
  --color-accent-foreground: oklch(0.167 0.024 66.558);
  --color-background: oklch(0.288 0.022 277.508);
  --color-foreground: oklch(0.977 0.007 106.545);
  --color-muted: oklch(0.394 0.032 275.524);
  --color-muted-foreground: oklch(0.879 0.006 275.524);
  --color-border: oklch(0.45 0.035 277.508);
  --color-card: oklch(0.32 0.025 277.508);
  --color-card-foreground: oklch(0.977 0.007 106.545);
  --color-popover: oklch(0.394 0.032 275.524);
  --color-popover-foreground: oklch(0.977 0.007 106.545);

  --color-note: oklch(0.70 0.14 240);
  --color-tip: oklch(0.75 0.13 160);
  --color-important: oklch(0.755 0.183 346.812);
  --color-warning: oklch(0.80 0.15 85);
  --color-caution: oklch(0.70 0.17 15);
}



@theme inline {
  --color-primary: var(--color-primary);
  --color-primary-foreground: var(--color-primary-foreground);
  --color-secondary: var(--color-secondary);
  --color-secondary-foreground: var(--color-secondary-foreground);
  --color-accent: var(--color-accent);
  --color-accent-foreground: var(--color-accent-foreground);
  --color-background: var(--color-background);
  --color-foreground: var(--color-foreground);
  --color-muted: var(--color-muted);
  --color-muted-foreground: var(--color-muted-foreground);
  --color-border: var(--color-border);
  --color-card: var(--color-card);
  --color-card-foreground: var(--color-card-foreground);
  --color-popover: var(--color-popover);
  --color-popover-foreground: var(--color-popover-foreground);

  --color-note: var(--color-note);
  --color-tip: var(--color-tip);
  --color-important: var(--color-important);
  --color-warning: var(--color-warning);
  --color-caution: var(--color-caution);

  /* Reading Progress */
  --reading-progress-bg: color-mix(
    in srgb,
    var(--color-border) 30%,
    transparent
  );
  --reading-progress-shadow: color-mix(
    in srgb,
    var(--color-primary) 30%,
    transparent
  );
  --reading-progress-height: 3px;
}
