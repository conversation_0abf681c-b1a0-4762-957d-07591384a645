---
title: "GitHub 风格提示框测试"
date: 2025-06-01T10:00:00+08:00
slug: test-alert
draft: false
description: "测试 GitHub 风格提示框和折叠功能"
tags: ["测试", "提示框", "markdown"]
categories: ["Github"]
---

# GitHub 风格提示框测试

本文用于测试新的 GitHub 风格提示框功能和折叠功能。

## 提示框语法

### 注意提示框

> [!NOTE]
> 这是一个注意提示框。用于显示用户应该注意的有用信息，即使在快速浏览内容时也应该注意。

### 提示提示框

> [!TIP]
> 这是一个提示提示框。提供有助于更好或更轻松地完成任务的建议。

### 重要提示框

> [!IMPORTANT]
> 这是一个重要提示框。显示用户成功完成任务所需的关键信息。

### 警告提示框

> [!WARNING]
> 这是一个警告提示框。由于潜在风险，需要用户立即注意的关键内容。

### 注意提示框

> [!CAUTION]
> 这是一个注意提示框。建议采取行动以避免负面后果。

## 折叠功能

### 可折叠的注意提示框

> [!NOTE]+ 可折叠的注意事项
> 这是一个可折叠的注意提示框。点击标题可以展开或折叠内容。
> 
> 这里可以包含更多详细信息，当用户需要时可以展开查看。

### 可折叠的提示提示框

> [!TIP]+ 高级使用技巧
> 这个折叠的提示框包含高级使用技巧：
> 
> 1. 首先做这个
> 2. 然后做那个
> 3. 最后完成这个步骤
> 
> 记住要按顺序执行这些步骤。

### 可折叠的重要提示框

> [!IMPORTANT]+ 重要配置信息
> 这些配置设置对系统正常运行至关重要：
> 
> ```yaml
> server:
>   port: 8080
>   host: localhost
> database:
>   url: mongodb://localhost:27017
>   name: myapp
> ```
> 
> 请确保所有设置都正确配置。

### 可折叠的警告提示框

> [!WARNING]+ 安全警告
> 在继续之前，请注意以下安全考虑：
> 
> - 确保所有密码都是强密码
> - 启用双因素认证
> - 定期更新系统
> - 不要在公共网络上执行敏感操作

### 可折叠的注意提示框

> [!CAUTION]+ 复杂内容示例
> 这个可折叠框包含复杂的 Markdown 内容：
> 
> #### 子标题
> 
> 这是一个包含[链接](https://example.com)和其他格式的段落。
> 
> ```javascript
> // 代码块示例
> function hello() {
>   console.log("你好，世界！");
> }
> ```
> 
> | 表格 | 示例 |
> |------|------|
> | 行1 | 数据1 |
> | 行2 | 数据2 |

## 普通引用块

这是一个普通的引用块，不是提示框：

> 这是一个标准的引用块。它不会被渲染为提示框，而是使用标准的引用块样式。
> 
> 支持多行内容和**格式化文本**。

## 多语言支持

提示框支持多种语言，标题会自动以当前语言显示：

> [!NOTE]
> 在中文环境中，这个标题会显示为"注意"。

> [!TIP]
> 在中文环境中，这个标题会显示为"提示"。

## 嵌套内容测试

> [!WARNING]+ 嵌套内容测试
> 这个提示框包含嵌套内容：
> 
> > 这是一个嵌套的引用块
> 
> - 列表项
>   - 嵌套列表项
>   - 另一个嵌套项
> 
> 1. 有序列表
>    1. 嵌套有序列表
>    2. 另一个嵌套项

## 使用场景示例

### 文档说明

> [!NOTE]
> 本功能从版本 2.0 开始可用。如果您使用的是较早版本，请先升级。

### 最佳实践

> [!TIP]+ 性能优化建议
> 为了获得最佳性能，建议：
> 
> - 使用 CDN 加速静态资源
> - 启用 gzip 压缩
> - 优化图片大小
> - 使用浏览器缓存

### 重要配置

> [!IMPORTANT]
> 修改配置文件后，必须重启服务才能生效。

### 安全提醒

> [!WARNING]
> 请勿在生产环境中使用默认密码。

### 破坏性操作

> [!CAUTION]
> 此操作将永久删除所有数据，且无法恢复。请确保已备份重要数据。

这些提示框可以帮助用户更好地理解内容的重要性和上下文，提升文档的可读性和用户体验。
