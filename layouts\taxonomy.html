{{ define "main" }}
  <!-- 面包屑导航 -->
  {{ partial "navigation/breadcrumb.html" . }}


  <!-- 页面标题和描述 -->
  <header class="mb-8">
    <h1 class="text-foreground mb-4 text-3xl font-bold">
      {{ if eq .Data.Singular "tag" }}
        {{ i18n "nav.tags" | default "标签" }}
      {{ else if eq .Data.Singular "category" }}
        {{ i18n "nav.categories" | default "分类" }}
      {{ else }}
        {{ .Title }}
      {{ end }}
    </h1>

    {{ if .Content }}
      <div class="prose prose-neutral dark:prose-invert mb-6 max-w-none">
        {{ .Content }}
      </div>
    {{ else }}
      <p class="text-muted-foreground mb-6">
        {{ if eq .Data.Singular "tag" }}
          {{ i18n "taxonomy.tags_desc" | default "浏览所有标签，发现感兴趣的内容。" }}
        {{ else if eq .Data.Singular "category" }}
          {{ i18n "taxonomy.categories_desc" | default "浏览所有分类，发现感兴趣的内容。" }}
        {{ else }}
          {{ i18n "taxonomy.desc" | default "浏览所有分类，发现感兴趣的内容。" }}
        {{ end }}
      </p>
    {{ end }}


    <!-- 统计信息 -->
    <div class="text-muted-foreground flex items-center gap-4 text-sm">
      <div class="flex items-center gap-1">
        {{ partial "features/icon.html" (dict "name" "tag" "size" "sm" "ariaLabel" "") }}
        <span>
          {{ if eq .Data.Singular "tag" }}
            {{ i18n "taxonomy.total_tags" (dict "count" (len .Data.Terms)) | default (printf "共 %d 个标签" (len .Data.Terms)) }}
          {{ else if eq .Data.Singular "category" }}
            {{ i18n "taxonomy.total_categories" (dict "count" (len .Data.Terms)) | default (printf "共 %d 个分类" (len .Data.Terms)) }}
          {{ else }}
            {{ i18n "taxonomy.total_terms" (dict "count" (len .Data.Terms)) | default (printf "共 %d 个%s" (len .Data.Terms) .Data.Singular) }}
          {{ end }}
        </span>
      </div>
    </div>
  </header>

  <!-- 分类法术语列表 -->
  {{ $terms := .Data.Terms.ByCount }}
  {{ if $terms }}
    <!-- 统一使用标签云布局 -->
    {{ $taxonomySingular := .Data.Singular }}
    <div class="flex flex-wrap gap-3">
      {{ $maxCount := 0 }}
      {{ range $terms }}
        {{ if gt .Count $maxCount }}
          {{ $maxCount = .Count }}
        {{ end }}
      {{ end }}

      {{ range $terms }}
        {{ $weight := div (mul .Count 4) $maxCount }}
        {{ $weight = add $weight 1 }}
        {{ $sizeClass := "" }}
        {{ $colorClass := "" }}

        {{ if eq $weight 1 }}
          {{ $sizeClass = "text-sm" }}
        {{ else if eq $weight 2 }}
          {{ $sizeClass = "text-base" }}
        {{ else if eq $weight 3 }}
          {{ $sizeClass = "text-lg" }}
        {{ else if eq $weight 4 }}
          {{ $sizeClass = "text-xl" }}
        {{ else }}
          {{ $sizeClass = "text-2xl" }}
        {{ end }}


        <a
          href="{{ .Page.RelPermalink }}"
          class="bg-card border-border group {{ $sizeClass }} hover:bg-primary/10 hover:text-primary hover:border-primary/50 focus:ring-primary/20 inline-flex items-center gap-2 rounded-lg border px-3 py-2 transition-all duration-300 ease-out hover:-translate-y-0.5 hover:scale-105 focus:ring-2 focus:outline-none"
          data-taxonomy="{{ $taxonomySingular }}">
          <span
            class="text-foreground group-hover:text-primary font-medium transition-colors duration-200">
            {{ .Page.LinkTitle }}
          </span>
          <span
            class="bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs font-medium">
            {{ .Count }}
          </span>
        </a>
      {{ end }}
    </div>
  {{ else }}
    <!-- 空状态 -->
    <div class="py-16 text-center">
      <div
        class="bg-muted/50 mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full">
        {{ partial "features/icon.html" (dict "name" "tag" "size" "xl" "ariaLabel" "") }}
      </div>
      <h2 class="text-foreground mb-3 text-xl font-medium">
        {{ i18n "taxonomy.no_terms" | default "暂无分类" }}
      </h2>
      <p class="text-muted-foreground mb-6">
        {{ i18n "taxonomy.no_terms_desc" | default "还没有创建任何分类，请稍后再来查看。" }}
      </p>
    </div>
  {{ end }}

{{ end }}
